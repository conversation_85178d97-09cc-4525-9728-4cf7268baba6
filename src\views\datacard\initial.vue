<template>
  <PageWrapper contentFullHeight class="scroll-smooth bg-white h-full">
    <!-- 按钮组 -->
    <div class="button-group">
      <a-button
        v-if="canEdit && (!archiveData?.topButtons || archiveData.topButtons.includes('新增'))"
        type="link"
        preIcon="ant-design:plus-circle-outlined"
        @click="handleAdd"
        >新增</a-button
      >
      <a-button
        v-if="canEdit && (!archiveData?.topButtons || archiveData.topButtons.includes('复制维护'))"
        type="link"
        preIcon="ant-design:copy-outlined"
        @click="handleCopy"
        >复制维护</a-button
      >
      <a-button
        v-if="canEdit && (!archiveData?.topButtons || archiveData.topButtons.includes('复制初装'))"
        type="link"
        preIcon="ant-design:copy-outlined"
        @click="handleCopyInitial"
        >复制初装</a-button
      >
      <a-button
        v-if="canEdit && (!archiveData?.topButtons || archiveData.topButtons.includes('提交审批'))"
        type="link"
        preIcon="ant-design:save-outlined"
        @click="handleSave"
        >提交审批</a-button
      >
      <a-button
        v-if="canEdit && (!archiveData?.topButtons || archiveData.topButtons.includes('删除'))"
        type="link"
        preIcon="ant-design:delete-outlined"
        @click="handleDelete"
        >删除</a-button
      >
      <a-button
        v-if="!archiveData?.topButtons || archiveData.topButtons.includes('卡片')"
        type="link"
        preIcon="ant-design:credit-card-outlined"
        @click="handleCard"
        >卡片</a-button
      >
      <a-button
        v-if="isFromHome"
        type="link"
        preIcon="ant-design:audit-outlined"
        @click="handleWorkflow"
        >工作流</a-button
      >

      <a-button
        v-if="!archiveData?.topButtons || archiveData.topButtons.includes('关闭')"
        type="link"
        preIcon="ant-design:close-circle-outlined"
        @click="closeCurrent"
        >关闭</a-button
      >
    </div>
    <!-- 分割线 -->
    <div class="horizontal-line"></div>
    <div class="flex h-full">
      <!-- 左侧树形菜单 -->
      <div class="w-64 border-r p-4 h-full">
        <BasicTree
          :showLine="true"
          :tree-data="convertHzFaMxToTreeDefaultRoot()"
          @select="handleSelect"
        />
      </div>

      <!-- 右侧表格内容 -->
      <div class="flex-1 p-4 pb-0 overflow-x-auto">
        <!-- 表格工具栏 -->
        <div class="flex justify-end items-center mb-2">
          <div class="flex items-center">
            <a-button type="link" @click="handleColumnSettings">列设置</a-button>
          </div>
        </div>
        <VxeBasicTable
          ref="tableRef"
          v-bind="gridOptions"
          v-on="gridEvents"
          :loading="tableLoading"
        >
          <template #empty>
            <p>没有更多数据了！</p>
          </template>
        </VxeBasicTable>
      </div>
    </div>
    <TableDetailsModel
      v-model:open="visible"
      v-if="visible"
      ref="tableDetailsModelRef"
      :tableHeader="processTableHeader(archiveData?.tableheader || [])"
      :tableValue="archiveData?.tablevalue || []"
      :rowData="selectedRow"
      :fieldname="archiveData?.fieldname || ''"
      :execname="archiveData?.execname || ''"
      :canEdit="canEdit"
      source="initial"
      @ok="handleOk"
      @cancel="handleTableCancel"
      :rowIndex="tableRowIndex"
      :optionType="optionType"
      @step="handleStep"
      @add="handleAdd"
    />

    <FileManagement
      v-model:visible="fileManagementVisible"
      :hzcode="hzcode"
      :rowid="selectedRowId"
    />

    <!-- 工作流弹窗 -->
    <WorkflowModal
      v-if="showWorkflowModal"
      v-model:open="showWorkflowModal"
      :danjbh="danjbhValue"
      :task-id="taskIdValue"
      :task-kind-id="taskKindIdValue"
      :group-id="1"
      :status="statusValue"
      :biz-id="bizIdValue"
      :djlx="djlxValue"
      :is-show-edit-btn="isShowEditBtnValue"
      :from-page="'initial'"
      @submit="handleApprovalSubmit"
      @close="showWorkflowModal = false"
    />

    <!-- 复制数据选择弹窗 -->
    <Modal
      v-model:open="copyDataModalVisible"
      title="选择要复制的数据"
      :width="1000"
      @ok="handleCopyDataConfirm"
      @cancel="handleCopyDataCancel"
      okText="确认复制"
      cancelText="取消"
      :maskClosable="false"
    >
      <div v-if="copyDataList.length > 0" style="height: 400px">
        <p style="margin-bottom: 16px">请选择要复制的数据（可多选）：</p>
        <VxeBasicTable ref="copyTableRef" v-bind="copyGridOptions" :height="350">
          <template #empty>
            <p>没有数据</p>
          </template>
        </VxeBasicTable>
      </div>
      <div v-else>
        <p>没有找到可复制的数据</p>
      </div>
    </Modal>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref, onMounted, reactive, nextTick, onUnmounted, computed } from 'vue';
  import { PageWrapper } from '@/components/Page';
  import { HzFaMxVo } from '@/api/datacard/model/datacardModel';
  import { Modal, Switch as _ASwitch } from 'ant-design-vue';
  import { createPrompt } from '@/components/Prompt';
  import {
    getArchiveDataApi,
    buildArchiveParams,
    addInitialDataApi,
    editInitialDataApi,
    deleteInitialDataApi,
    saveInitialDataApi,
    checkArchiveDataApi,
    updateMxColWidthApi,
    getArchiveDataByIdsApi,
  } from '@/api/datacard/index';
  import { useRoute, useRouter } from 'vue-router';
  import { convertHzFaMxToTree } from './utils';
  import { VxeBasicTable, VxeGridInstance, VxeGridListeners } from '@/components/VxeTable';
  import TableDetailsModel from './components/TableDetailsModel.vue';
  import FileManagement from './components/FileManagement.vue';
  import WorkflowModal from '../query/components/WorkflowModal.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useTabs } from '@/hooks/web/useTabs';
  import { BasicTree } from '@/components/Tree';
  import type { TreeActionType } from '@/components/Tree/src/types/tree';
  import { submitApproval } from '@/api/workflow';
  import { useWorkflowStore } from '@/store/modules/workflow';

  // 使用内联类型定义，避免重复定义
  interface ArchiveData {
    fieldname: string;
    hzfamx: HzFaMxVo[];
    tableheader: Array<{
      fdname: string;
      chnname: string;
      fdtype: string;
      fdsize: number;
      fddec: number;
      displayorder: string | number;
      dropdownoptions: any;
      customtag: string | null;
      isnull: boolean;
      isdsp: boolean;
      isriqi?: boolean;
      isthumbnail?: boolean;
      isradiobutton?: boolean;
      ismultichoice?: boolean;
      tagfddisplayorder?: string | number | null;
    }>;
    tablevalue: any[];
    pagedData?: {
      total: number;
      pageNum: number;
      pageSize: number;
      data: any[];
    };
    execname?: string;
    topButtons?: string[]; // 添加 topButtons 字段
  }

  const tableRef = ref<VxeGridInstance>();
  const tableRowIndex = ref(0);
  const route = useRoute();
  const _router = useRouter(); // 重命名为_router来避免lint警告
  const visible = ref(false);
  const fileManagementVisible = ref(false);
  const selectedRow = ref<any>(null);
  const selectedRowId = ref('');
  const selectedTreeKey = ref('');
  const hzcode = ref('');
  const archiveData = ref<ArchiveData>();
  const _columns = ref<any[]>([]);
  // 树形数据
  const _treeRef = ref<Nullable<TreeActionType>>(null); // 重命名为_treeRef以避免unused warning
  const _filteredData = ref<any[]>([]); // 重命名为_filteredData以避免lint警告
  const _formData = ref({});
  const { setTitle, closeCurrent } = useTabs();
  const { createMessage } = useMessage();
  const currentIndex = ref(0);
  const gridEvents: VxeGridListeners = {
    cellDblclick({ row, rowIndex }) {
      // 如果不允许编辑（isShowEditBtn=0），则不响应双击事件
      if (!canEdit.value) {
        return;
      }

      // 检查row是否为空对象
      if (Object.keys(row).filter((key) => key !== '_X_ROW_KEY').length === 0) {
        // 如果是空行，设置为新增模式
        optionType.value = 'add';

        // 创建一个新的空行对象，而不是修改当前行
        const newRow = {};

        // 如果存在主键字段，设置主键值
        if (archiveData.value?.fieldname) {
          newRow[archiveData.value.fieldname] = selectedTreeKey.value || '';
        }

        // 设置为选中行
        selectedRow.value = newRow;
      } else {
        // 如果不是空行，设置为编辑模式
        optionType.value = 'edit';
        selectedRow.value = row;
      }

      // 记录行索引并打开弹窗
      tableRowIndex.value = rowIndex;
      currentIndex.value = rowIndex + 1;
      visible.value = true;
    },
    cellClick(data) {
      const { row, rowIndex } = data;
      tableRef.value?.setCurrentRow(row);
      const currentPage = tableRef.value?.getProxyInfo()?.pager?.currentPage || 1;
      const pageSize = tableRef.value?.getProxyInfo()?.pager?.pageSize || 20;
      currentIndex.value = (currentPage - 1) * pageSize + rowIndex + 1;
    },
    keydown: () => {},
    pageChange: ({ currentPage, pageSize }) => {
      currentIndex.value = (currentPage - 1) * pageSize + 1;
    },
  };

  const optionType = ref('');
  const tableLoading = ref(false);

  // 在其他ref变量定义下面添加初始化标志
  const isInitialized = ref(false);

  // 组件引用
  const tableDetailsModelRef = ref<InstanceType<typeof TableDetailsModel> | null>(null);

  const canEdit = ref(true); // 默认可以编辑
  const isFromHome = ref(false); // 是否从首页跳转过来

  // 工作流相关
  const showWorkflowModal = ref(false);
  const taskId = ref(''); // 当前任务ID
  const taskKindId = ref(''); // 任务种类ID
  const workflowStore = useWorkflowStore();

  // 复制数据选择弹窗相关状态
  const copyDataModalVisible = ref(false);
  const copyDataList = ref<any[]>([]);
  const selectedCopyData = ref<any[]>([]);
  const copyTableRef = ref<VxeGridInstance>();

  // 计算属性，处理工作流弹窗需要的参数
  const danjbhValue = computed(() => String(route.query.danjbh || ''));
  const taskIdValue = computed(() => String(route.query.taskId || ''));
  const taskKindIdValue = computed(() => String(route.query.taskKindId || ''));
  const statusValue = computed(() => String(route.query.status || ''));
  const bizIdValue = computed(() => String(route.query.bizId || route.query.danjbh || ''));
  const djlxValue = computed(() => String(route.query.djlx || ''));
  const isShowEditBtnValue = computed(() => String(route.query.isShowEditBtn || ''));

  // 更新gridOptions定义
  const gridOptions = reactive({
    id: 'InvoiceTable',
    border: true,
    size: 'mini',
    padding: false,
    scrollX: {
      enabled: true,
    },
    rowConfig: {
      height: 40,
    },
    columnConfig: {
      resizable: true, // 启用列宽调整
    },
    height: '500',
    customConfig: {
      storage: true,
      mode: 'popup',
      // 添加对拖拽的支持
      checkMethod: () => true, // 所有列都可以被选中/取消选中
      dragSort: true, // 启用拖拽排序
      sortMethod: () => true, // 允许所有排序操作
      showIcon: true, // 显示图标
      updateStore({ storeData }) {
        console.log('保存列宽和顺序数据:', storeData);

        // 检查数据完整性
        if (!storeData || !storeData.sortData) {
          console.warn('列设置数据不完整，无法保存');
          return Promise.resolve({ code: '0000', message: '数据不完整，跳过保存' });
        }

        // 确保数据格式正确，再调用API
        return updateMxColWidthApi({
          djlx: hzcode.value,
          pagetype: 'init' + hzcode.value, // 添加"init"前缀以区别于单据维护页面
          flds: transformColumnData(storeData),
        });
      },
      restoreStore: async () => {
        // 从服务器恢复列设置
        // 暂时留空，需要时可以实现
        return '';
      },
    },
    pagerConfig: {
      enabled: true,
      pageSize: 10,
      pageSizes: [10, 20, 50, 100],
      total: 0,
      layouts: ['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total'],
    },
    toolbarConfig: {
      enabled: false,
    },
    keyboardConfig: {
      isArrow: true,
      isTab: true,
      isDel: true,
    },
    columns: [] as any[],
    data: [],
    proxyConfig: {
      props: {
        result: 'items',
        total: 'total',
      },
      ajax: {
        // 添加initialized判断，防止自动加载
        query: async ({ page }) => {
          // 如果还未初始化，返回空数据
          if (!isInitialized.value) {
            return { items: [{}], total: 0 };
          }

          try {
            tableLoading.value = true;
            // 请求后端分页数据
            const pageNum = page.currentPage;
            // 获取单据编号(如果从首页跳转过来)
            const danjbh = route.query.danjbh as string;

            // 从后端获取数据，使用统一接口
            const filters: Record<string, any> = {};

            // 如果classfrule不为'ALL'且存在，则添加到过滤条件中
            if (selectedTreeKey.value && selectedTreeKey.value !== 'ALL') {
              filters.classfrule = selectedTreeKey.value;
            }

            let params = buildArchiveParams(filters);
            if (danjbh) {
              params = [
                ...params,
                {
                  field: 'danjbh',
                  fieldValue: danjbh,
                  matchType: 'EXACT',
                },
              ];
            }
            const result = await getArchiveDataApi({
              hzcode: hzcode.value,
              type: '0', // 资料卡片初装
              pageNum: pageNum,
              pageSize: 10, // 默认页大小
              params: params.length > 0 ? params : undefined,
            });

            if (!result) {
              console.error('获取数据失败:', result?.message || '未知错误');
              return { items: [{}], total: 0 };
            }

            // 更新archiveData
            archiveData.value = result;

            // 如果是第一次加载，设置表格列
            if (!gridOptions.columns || gridOptions.columns.length === 0) {
              if (archiveData.value!.tableheader) {
                // 先按tagfddisplayorder排序表头
                const sortedHeaders = [...archiveData.value!.tableheader].sort((a, b) => {
                  // 如果tagfddisplayorder为null，则放到最后
                  if (a.tagfddisplayorder === null) return 1;
                  if (b.tagfddisplayorder === null) return -1;
                  return Number(a.tagfddisplayorder) - Number(b.tagfddisplayorder);
                });

                gridOptions.columns = sortedHeaders.map((item) => ({
                  field: item.fdname,
                  title: item.chnname,
                  width:
                    (item as any).colwidth || Math.max(item.fdsize * 10, item.chnname.length * 20),
                  key: item.fdname,
                  visible: item.isdsp,
                  showOverflow: 'ellipsis', // 为每列添加省略号显示
                  sortable: true, // 启用排序
                }));
              }
            }

            // 获取要显示的数据（创建新数组避免直接修改响应式数据）
            let displayData = [...(result.pagedData?.data || [])];

            // 处理返回的数据
            const returnData = {
              items: displayData.length ? displayData : [{}],
              total: result.pagedData?.total || 0,
            };

            // 如果是第一页，设置默认选中第一行
            if (pageNum === 1 && displayData.length > 0) {
              const firstRow = displayData[0];
              if (Object.keys(firstRow).filter((key) => key !== '_X_ROW_KEY').length > 0) {
                // 使用nextTick确保在DOM更新后执行
                nextTick(() => {
                  selectedRow.value = firstRow;
                  tableRowIndex.value = 0;
                  tableRef.value?.setCurrentRow(firstRow);
                  currentIndex.value = 1;
                });
              }
            }

            return returnData;
          } catch (error) {
            console.error('获取数据出错:', error);
            return { items: [{}], total: 0 };
          } finally {
            tableLoading.value = false;
          }
        },
      },
    },
  });

  // 复制数据表格配置
  const copyGridOptions = reactive({
    id: 'CopyDataTable',
    border: true,
    size: 'mini',
    padding: false,
    height: 350,
    rowConfig: {
      height: 40,
      isHover: true,
    },
    columnConfig: {
      resizable: true,
    },
    checkboxConfig: {
      labelField: '',
      checkMethod: () => true,
      trigger: 'cell',
    },
    toolbarConfig: {
      enabled: false,
    },
    columns: [] as any[],
    proxyConfig: {
      props: {
        result: 'items',
        total: 'total',
      },
      ajax: {
        query: async () => {
          try {
            // 返回复制数据
            return {
              items: copyDataList.value || [],
              total: copyDataList.value?.length || 0,
            };
          } catch (error) {
            console.error('获取复制数据出错:', error);
            return { items: [], total: 0 };
          }
        },
      },
    },
  });

  // 修改init函数
  async function init() {
    try {
      tableLoading.value = true;
      setTitle(route.query.title ? (route.query.title as string) : '资料卡片初装');
      // 从url中获取hzcode参数
      hzcode.value = route.query.hzcode as string;
      if (!hzcode.value) {
        // 如果没有路由参数，尝试从路径中获取
        const pathArray = route.path.split('/');
        hzcode.value = pathArray[pathArray.length - 1];
      }

      // 检查是否是从首页跳转过来的
      const fromHome = route.query.fromHome === 'true';
      const danjbh = route.query.danjbh as string;
      if (fromHome) {
        // 如果是从首页跳转过来的，设置isFromHome为true
        isFromHome.value = true;
        console.log('从首页工作流跳转过来，hzcode:', hzcode.value, 'danjbh:', danjbh);
      }

      // 判断URL中是否传入isShowEditBtn参数
      const isShowEditBtn = route.query.isShowEditBtn;
      if (isShowEditBtn !== undefined) {
        // 如果传入了isShowEditBtn参数，判断其值
        canEdit.value = isShowEditBtn === '1';
      } else {
        // 如果没有传入，则默认可以编辑
        canEdit.value = true;
      }

      // 等待DOM渲染完成
      await nextTick();

      // 设置初始化标志
      isInitialized.value = true;

      // 主动触发一次数据加载
      // await tableRef.value?.commitProxy('query');
    } catch (error) {
      console.error('初始化数据出错:', error);
      createMessage.error('初始化数据出错');
    } finally {
      tableLoading.value = false;
    }
  }

  const handleOk = async (formData, _rowIndex) => {
    try {
      // 剔除_X_ROW_KEY字段
      const cleanedData = { ...formData };
      delete cleanedData._X_ROW_KEY;

      // 检查是否需要进行数据校验
      if (archiveData.value?.execname) {
        // 调用校验接口
        const checkResult = await checkArchiveDataApi({
          execname: archiveData.value.execname,
          data: [{ rowno: _rowIndex, dataMap: cleanedData }],
        });

        // 如果checkResult不为null，表示校验失败
        if (checkResult) {
          // 校验失败时显示错误信息并终止保存操作
          let errorMessage = '数据校验失败';
          let detailMessage = '';

          // 如果有详细错误信息，显示具体字段错误
          //const errorData = Array.isArray(checkResult) ? checkResult[0] : checkResult;
          for (let i = 0; i < checkResult.length; i++) {
            if (checkResult[i].rowno && checkResult[i].setfld && checkResult[i].sqlMessage) {
              detailMessage =
                detailMessage +
                `【第${parseInt(checkResult[i].rowno) + 1}行】${checkResult[i].setfld}错误详情：${checkResult[i].sqlMessage}` +
                '\n';
            }
          }

          Modal.warn({
            title: `校验失败`,
            content: detailMessage || errorMessage,
            okText: '确定',
          });
          return;
        }
      }

      if (optionType.value === 'add' || optionType.value === 'copy') {
        // 新增数据或复制数据 - 只包含后端接受的字段
        cleanedData.classfrule =
          selectedTreeKey.value && selectedTreeKey.value !== 'ALL'
            ? selectedTreeKey.value
            : undefined;
        const dataList = [cleanedData];
        // 获取单据编号(如果从首页跳转过来)
        const danjbh = route.query.danjbh as string;

        const response = await addInitialDataApi({
          hzcode: hzcode.value,
          dataList,
          danjbh: isFromHome.value && danjbh ? danjbh : undefined, // 从首页跳转时携带danjbh
        });

        // 如果后端返回了数据，需要更新本地数据
        if (response && response.data) {
          // 合并后端返回的数据到表格中
          const returnedData = Array.isArray(response.data) ? response.data[0] : response.data;
          if (returnedData) {
            Object.assign(cleanedData, returnedData);
          }
        }

        createMessage.success(optionType.value === 'copy' ? '复制成功' : '新增成功');
      } else if (optionType.value === 'edit') {
        // 编辑数据
        const dataList = [cleanedData];
        await editInitialDataApi({
          hzcode: hzcode.value,
          dataList,
        });
        createMessage.success('修改成功');
        visible.value = false;
      } else if (optionType.value === 'delete') {
        // 删除逻辑已在handleDelete处理
        return;
      }
      setTimeout(() => {
        tableDetailsModelRef.value?.clearFormData();
        selectedRow.value = {};
      }, 300);

      // 只有在初始化后才重新加载数据
      if (isInitialized.value) {
        // 重新加载数据
        tableRef.value?.commitProxy('query', {
          page: { currentPage: 1, pageSize: gridOptions.pagerConfig.pageSize },
        });
      }
    } catch (error) {
      console.error('操作失败:', error);
      createMessage.error('操作失败');
    }
  };

  const handleDelete = async () => {
    // 如果不能编辑，直接返回
    if (!canEdit.value) {
      Modal.warn({
        title: '操作错误',
        content: '当前模式下不允许编辑！',
        okText: '确定',
      });
      return;
    }

    // 获取当前选中行
    const currentRow = tableRef.value?.getCurrentRecord();
    const currentRowIndex = tableRef.value?.getRowIndex(currentRow);

    if (
      currentRowIndex !== undefined &&
      currentRowIndex > -1 &&
      currentRow &&
      archiveData.value?.fieldname
    ) {
      try {
        // 确认删除
        optionType.value = 'delete';
        selectedRow.value = currentRow;

        // 显示确认对话框
        Modal.confirm({
          title: '确认删除',
          content: '确定要删除该条记录吗？',
          okText: '确定',
          cancelText: '取消',
          onOk: async () => {
            try {
              // 获取主键值
              const rowId = currentRow[archiveData.value!.fieldname];

              // 调用删除API
              await deleteInitialDataApi({
                hzcode: hzcode.value,
                type: '0', // 资料卡片初装
                delDataList: [rowId],
              });

              createMessage.success('删除成功');

              // 只有在初始化后才重新加载数据
              if (isInitialized.value) {
                // 重新加载数据
                tableRef.value?.commitProxy('query', {
                  page: { currentPage: 1, pageSize: gridOptions.pagerConfig.pageSize },
                });
              }
            } catch (error) {
              console.error('删除失败:', error);
              createMessage.error('删除失败');
            }
          },
        });
      } catch (error) {
        console.error('删除操作失败:', error);
        createMessage.error('删除操作失败');
      }
    } else {
      Modal.warn({
        title: '操作错误',
        content: '请先选择要删除的行！',
        okText: '确定',
      });
    }
  };

  onMounted(() => {
    // 如果仍然没有获取到hzcode，跳转到首页
    console.log('route.query.hzcode', route.query.hzcode);
    if (!route.query.hzcode) {
      console.warn('未获取到hzcode参数，跳转到首页');
      window.location.href = '/';
      return;
    }
    init();

    // 获取任务ID和任务种类ID
    taskId.value = String(route.query.taskId || '');
    taskKindId.value = String(route.query.taskKindId || '');

    // 组件卸载时清理store数据
    onUnmounted(() => {
      workflowStore.clearCurrentWorkflowItem();
    });
  });

  // 转换商品分类数据为树形结构
  function convertHzFaMxToTreeDefaultRoot() {
    // 创建一个包含"全部商品"节点的结果数组
    const result: Array<{ title: string; key: string; children: any[] }> = [
      {
        title: '全部',
        key: 'ALL',
        children: [],
      },
    ];
    const data = convertHzFaMxToTree(archiveData.value?.hzfamx ?? null);
    if (data) {
      // 将hzfamx数据添加到result中
      result.push(...(data as any));
    }

    return result;
  }

  async function handleSave() {
    // 如果不能编辑，直接返回
    if (!canEdit.value) {
      Modal.warn({
        title: '操作错误',
        content: '当前模式下不允许编辑！',
        okText: '确定',
      });
      return;
    }

    // 在提交审批前进行表单验证
    try {
      // 获取所有表格数据进行验证
      const allTableData = await getAllTableDataForValidation();

      if (!allTableData || allTableData.length === 0) {
        Modal.warn({
          title: '验证失败',
          content: '没有找到需要提交审批的数据，请先添加数据！',
          okText: '确定',
        });
        return;
      }

      // 验证必填字段
      const validationResult = validateRequiredFields(allTableData);

      if (!validationResult.isValid) {
        // 显示验证错误信息
        showValidationErrors(validationResult.errors);
        return;
      }

      // 如果有后端校验规则，进行后端校验
      if (archiveData.value?.execname) {
        const backendValidationResult = await validateDataWithBackend(allTableData);
        if (!backendValidationResult.isValid) {
          showBackendValidationErrors(backendValidationResult.errors);
          return;
        }
      }

      // 所有验证通过，执行提交审批
      await saveInitialDataApi({ hzcode: hzcode.value });

      // 刷新表格数据
      if (isInitialized.value) {
        tableRef.value?.commitProxy('query', {
          page: { currentPage: 1, pageSize: gridOptions.pagerConfig.pageSize },
        });
      }

      Modal.success({
        title: '提交审批成功',
        content: '数据已成功提交审批！',
        okText: '确定',
      });
    } catch (error) {
      console.error('提交审批失败:', error);
      Modal.error({
        title: '提交审批失败',
        content: '提交审批时发生错误，请重试！',
        okText: '确定',
      });
    }
  }

  // 获取所有表格数据用于验证
  async function getAllTableDataForValidation() {
    try {
      // 获取所有页面的数据，不使用分页限制
      const filters: Record<string, any> = {};

      // 如果classfrule不为'ALL'且存在，则添加到过滤条件中
      if (selectedTreeKey.value && selectedTreeKey.value !== 'ALL') {
        filters.classfrule = selectedTreeKey.value;
      }

      let params = buildArchiveParams(filters);

      // 如果是从首页跳转过来的，添加单据编号过滤
      const danjbh = route.query.danjbh as string;
      if (danjbh) {
        params = [
          ...params,
          {
            field: 'danjbh',
            fieldValue: danjbh,
            matchType: 'EXACT',
          },
        ];
      }

      const result = await getArchiveDataApi({
        hzcode: hzcode.value,
        type: '0', // 资料卡片初装
        pageNum: 1,
        pageSize: 9999, // 获取所有数据
        params: params.length > 0 ? params : undefined,
      });

      return result?.pagedData?.data || [];
    } catch (error) {
      console.error('获取表格数据失败:', error);
      return [];
    }
  }

  // 验证必填字段
  function validateRequiredFields(tableData: any[]) {
    const errors: Array<{
      rowIndex: number;
      fieldName: string;
      fieldLabel: string;
      value: any;
    }> = [];

    if (!archiveData.value?.tableheader) {
      return {
        isValid: false,
        errors: [{ rowIndex: 0, fieldName: '', fieldLabel: '表头配置', value: '' }],
      };
    }

    // 获取必填字段列表
    const requiredFields = archiveData.value.tableheader.filter((header) => !header.isnull);

    // 验证每一行数据
    tableData.forEach((row, index) => {
      // 跳过空行
      if (!row || Object.keys(row).filter((key) => key !== '_X_ROW_KEY').length === 0) {
        return;
      }

      // 检查每个必填字段
      requiredFields.forEach((field) => {
        const fieldValue = row[field.fdname];

        // 检查字段值是否为空
        const isEmpty =
          fieldValue === undefined ||
          fieldValue === null ||
          fieldValue === '' ||
          (Array.isArray(fieldValue) && fieldValue.length === 0);

        // 对于开关字段，false也是有效值
        const isValidRadioButton =
          field.isradiobutton && (fieldValue === false || fieldValue === '否');

        if (isEmpty && !isValidRadioButton) {
          errors.push({
            rowIndex: index + 1,
            fieldName: field.fdname,
            fieldLabel: field.chnname,
            value: fieldValue,
          });
        }
      });
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // 显示验证错误信息
  function showValidationErrors(
    errors: Array<{
      rowIndex: number;
      fieldName: string;
      fieldLabel: string;
      value: any;
    }>,
  ) {
    // 按行号分组错误信息
    const errorsByRow = errors.reduce(
      (acc, error) => {
        if (!acc[error.rowIndex]) {
          acc[error.rowIndex] = [];
        }
        acc[error.rowIndex].push(error);
        return acc;
      },
      {} as Record<number, typeof errors>,
    );

    // 构建错误信息HTML
    const errorList = Object.entries(errorsByRow)
      .map(([rowIndex, rowErrors]) => {
        const fieldErrors = rowErrors
          .map(
            (error) => `<span style="color: #ff4d4f; font-weight: 500;">${error.fieldLabel}</span>`,
          )
          .join('、');

        return `<div style="margin-bottom: 12px; padding: 8px; border-left: 4px solid #ff4d4f; background-color: #fff2f0;">
          <div style="font-weight: 600; color: #262626; margin-bottom: 4px;">第 ${rowIndex} 行</div>
          <div style="color: #595959;">以下必填字段未填写：${fieldErrors}</div>
        </div>`;
      })
      .join('');

    const content = `
      <div style="max-height: 400px; overflow-y: auto;">
        <div style="margin-bottom: 16px; color: #262626; font-size: 14px;">
          <strong>数据验证失败，发现 ${errors.length} 个必填字段未填写：</strong>
        </div>
        ${errorList}
        <div style="margin-top: 16px; padding: 12px; background-color: #f6ffed; border: 1px solid #b7eb8f; border-radius: 4px;">
          <div style="color: #389e0d; font-weight: 500;">💡 提示：</div>
          <div style="color: #52c41a; margin-top: 4px;">请补充完整所有必填信息后，再次点击"提交审批"按钮。</div>
        </div>
      </div>
    `;

    Modal.warn({
      title: '表单验证失败',
      content: content,
      okText: '确定',
      width: 600,
    });
  }

  // 后端数据校验
  async function validateDataWithBackend(tableData: any[]) {
    try {
      // 准备校验数据
      const validationData = tableData.map((row, index) => {
        const cleanedRow = { ...row };
        delete cleanedRow._X_ROW_KEY;
        return {
          rowno: index,
          dataMap: cleanedRow,
        };
      });

      // 调用后端校验接口
      const checkResult = await checkArchiveDataApi({
        execname: archiveData.value!.execname!,
        data: validationData,
      });

      if (checkResult && checkResult.length > 0) {
        return {
          isValid: false,
          errors: checkResult,
        };
      }

      return { isValid: true, errors: [] };
    } catch (error) {
      console.error('后端数据校验失败:', error);
      return {
        isValid: false,
        errors: [{ rowno: 0, setfld: '系统错误', sqlMessage: '后端校验服务异常，请稍后重试' }],
      };
    }
  }

  // 显示后端验证错误信息
  function showBackendValidationErrors(errors: any[]) {
    const errorList = errors
      .map(
        (error, _index) =>
          `<div style="margin-bottom: 12px; padding: 8px; border-left: 4px solid #ff4d4f; background-color: #fff2f0;">
          <div style="font-weight: 600; color: #262626; margin-bottom: 4px;">
            第 ${parseInt(error.rowno) + 1} 行 - ${error.setfld || '未知字段'}
          </div>
          <div style="color: #ff4d4f;">${error.sqlMessage || '校验失败'}</div>
        </div>`,
      )
      .join('');

    const content = `
      <div style="max-height: 400px; overflow-y: auto;">
        <div style="margin-bottom: 16px; color: #262626; font-size: 14px;">
          <strong>数据校验失败，发现以下业务逻辑错误：</strong>
        </div>
        ${errorList}
      </div>
    `;

    Modal.warn({
      title: '业务校验失败',
      content: content,
      okText: '确定',
      width: 600,
    });
  }

  function handleAdd() {
    // 如果不能编辑，直接返回
    if (!canEdit.value) {
      Modal.warn({
        title: '操作错误',
        content: '当前模式下不允许编辑！',
        okText: '确定',
      });
      return;
    }

    // 校验是否选中分类树
    if (!selectedTreeKey.value || selectedTreeKey.value === 'ALL') {
      Modal.warn({
        title: '操作错误',
        content: '请先选择一个分类树节点！',
        okText: '确定',
      });
      return;
    }

    // 判断选中的节点是否为叶子节点
    const isLeafNode = (key) => {
      // 在树形数据中查找对应节点
      const findNode = (nodes, targetKey) => {
        for (let node of nodes) {
          if (node.key === targetKey) {
            return node.children && node.children.length > 0 ? false : true;
          }
          if (node.children && node.children.length > 0) {
            const found = findNode(node.children, targetKey);
            if (found !== undefined) return found;
          }
        }
        return undefined;
      };

      return findNode(convertHzFaMxToTreeDefaultRoot(), key);
    };

    if (!isLeafNode(selectedTreeKey.value)) {
      Modal.warn({
        title: '操作错误',
        content: '新增操作只能在最底层的分类节点下进行！',
        okText: '确定',
      });
      return;
    }

    if (!archiveData.value) {
      console.error('archiveData不存在，无法执行新增操作');
      return;
    }

    optionType.value = 'add';
    const tableInstance = tableRef.value;
    if (!tableInstance) return;

    // 清空选中行，确保每次新增时不保留上次的输入内容
    selectedRow.value = {};

    // 如果有主键字段，设置默认值
    if (archiveData.value?.fieldname) {
      selectedRow.value[archiveData.value.fieldname] = selectedTreeKey.value || '';
    }

    // 打开弹窗
    visible.value = true;
  }

  function handleCopy() {
    // 如果不能编辑，直接返回
    if (!canEdit.value) {
      Modal.warn({
        title: '操作错误',
        content: '当前模式下不允许编辑！',
        okText: '确定',
      });
      return;
    }

    if (!archiveData.value) {
      console.error('archiveData不存在，无法执行复制操作');
      return;
    }

    // 弹出输入框让用户输入维护ID
    const promptInstance = createPrompt({
      title: '复制资料数据',
      label: '维护ID',
      required: true,
      inputType: 'InputTextArea',
      defaultValue: '',
      width: '500px',
      onOK: async (inputValue: string) => {
        try {
          if (!inputValue || !inputValue.trim()) {
            createMessage.warn('请输入维护ID');
            return;
          }

          // 解析输入的ID（支持逗号分隔）
          const ids = inputValue
            .split(',')
            .map((id) => id.trim())
            .filter((id) => id.length > 0);

          if (ids.length === 0) {
            createMessage.warn('请输入有效的维护ID');
            return;
          }

          // 调用API获取资料维护数据
          const response = await getArchiveDataByIdsApi({
            hzcode: hzcode.value,
            ids: ids,
          });

          if (!response || !response.tablevalue || response.tablevalue.length === 0) {
            Modal.warn({
              title: '提示',
              content: '未找到对应的资料数据，请检查维护ID是否正确',
              okText: '确定',
            });
            return;
          }

          // 更新复制数据列表
          copyDataList.value = response.tablevalue;
          selectedCopyData.value = [];

          // 配置复制表格的列
          setupCopyTableColumns(response.tableheader || archiveData.value?.tableheader || []);

          // 打开复制数据选择弹窗
          copyDataModalVisible.value = true;

          // 使用nextTick确保弹窗打开后再加载数据
          nextTick(() => {
            // 触发表格数据加载
            copyTableRef.value?.commitProxy('query');
          });
        } catch (error) {
          console.error('获取资料维护数据失败:', error);
          Modal.error({
            title: '获取数据失败',
            content: '获取资料维护数据失败，请稍后重试',
            okText: '确定',
          });
        }
      },
    });

    // 将弹窗添加到页面中
    document.body.appendChild(promptInstance.$el);
  }

  async function handleCopyInitial() {
    // 如果不能编辑，直接返回
    if (!canEdit.value) {
      Modal.warn({
        title: '操作错误',
        content: '当前模式下不允许编辑！',
        okText: '确定',
      });
      return;
    }

    if (!archiveData.value) {
      console.error('archiveData不存在，无法执行复制操作');
      return;
    }

    try {
      // 获取当前表格的所有数据
      const currentTableData = await getAllTableDataForValidation();

      if (!currentTableData || currentTableData.length === 0) {
        Modal.warn({
          title: '提示',
          content: '当前表格中没有数据可以复制',
          okText: '确定',
        });
        return;
      }

      // 过滤掉空行
      const validData = currentTableData.filter(
        (row) => row && Object.keys(row).filter((key) => key !== '_X_ROW_KEY').length > 0,
      );

      if (validData.length === 0) {
        Modal.warn({
          title: '提示',
          content: '当前表格中没有有效数据可以复制',
          okText: '确定',
        });
        return;
      }

      // 更新复制数据列表
      copyDataList.value = validData;
      selectedCopyData.value = [];

      // 配置复制表格的列
      setupCopyTableColumns(archiveData.value?.tableheader || []);

      // 打开复制数据选择弹窗
      copyDataModalVisible.value = true;

      // 使用nextTick确保弹窗打开后再加载数据
      nextTick(() => {
        // 触发表格数据加载
        copyTableRef.value?.commitProxy('query');
      });
    } catch (error) {
      console.error('获取当前表格数据失败:', error);
      Modal.error({
        title: '获取数据失败',
        content: '获取当前表格数据失败，请稍后重试',
        okText: '确定',
      });
    }
  }

  // 配置复制表格的列
  function setupCopyTableColumns(tableHeaders: any[]) {
    if (!tableHeaders || tableHeaders.length === 0) return;

    // 第一列为复选框列
    const columns: any[] = [
      {
        type: 'checkbox',
        width: 60,
        align: 'center',
      },
    ];

    // 获取当前初装数据的字段列表
    const currentDataFields = archiveData.value?.tableheader?.map((header) => header.fdname) || [];

    // 添加数据列，只显示可见的字段且在当前初装数据中存在的字段
    const visibleHeaders = tableHeaders.filter((header) => header.isdsp !== false);

    // 进一步过滤，只保留在当前初装数据中存在的字段
    const existingHeaders = visibleHeaders.filter((header) =>
      currentDataFields.includes(header.fdname),
    );

    existingHeaders.forEach((header) => {
      columns.push({
        field: header.fdname,
        title: header.chnname,
        width: Math.max(header.fdsize * 10, header.chnname.length * 20, 100),
        showOverflow: 'ellipsis',
        sortable: true,
      });
    });

    copyGridOptions.columns = columns;
  }

  // 处理复制数据确认
  async function handleCopyDataConfirm() {
    if (!copyTableRef.value) {
      createMessage.warn('表格未初始化');
      return;
    }

    // 获取选中的行数据
    const checkedRows = copyTableRef.value.getCheckboxRecords();

    if (!checkedRows || checkedRows.length === 0) {
      createMessage.warn('请至少选择一条数据进行复制');
      return;
    }

    // 关闭选择弹窗
    copyDataModalVisible.value = false;

    try {
      // 直接执行批量复制到数据库
      await executeBatchCopyToDatabase(checkedRows);
    } catch (error) {
      console.error('复制数据失败:', error);
      createMessage.error('复制数据失败');
    }
  }

  // 处理复制数据取消
  function handleCopyDataCancel() {
    copyDataModalVisible.value = false;
    copyDataList.value = [];
    selectedCopyData.value = [];
    copyGridOptions.columns = [];

    // 清除表格选中状态
    if (copyTableRef.value) {
      copyTableRef.value.clearCheckboxRow();
    }
  }

  // 执行批量数据复制到数据库
  async function executeBatchCopyToDatabase(selectedRows: any[]) {
    if (!archiveData.value) return;

    try {
      // 获取当前初装数据的字段列表
      const currentDataFields =
        archiveData.value?.tableheader?.map((header) => header.fdname) || [];

      const batchData = selectedRows.map((sourceData) => {
        const copiedData = { ...sourceData };
        delete copiedData._X_ROW_KEY;

        // 只保留在当前初装数据中存在的字段
        const filteredData: any = {};
        Object.keys(copiedData).forEach((key) => {
          if (currentDataFields.includes(key)) {
            filteredData[key] = copiedData[key];
          }
        });

        // 如果有主键字段，清除原有主键值
        if (archiveData.value?.fieldname) {
          delete filteredData[archiveData.value.fieldname];
        }

        // 设置分类字段
        if (selectedTreeKey.value && selectedTreeKey.value !== 'ALL') {
          filteredData.classfrule = selectedTreeKey.value;
        }

        return filteredData;
      });

      // 获取单据编号(如果从首页跳转过来)
      const danjbh = route.query.danjbh as string;

      // 批量新增数据
      await addInitialDataApi({
        hzcode: hzcode.value,
        dataList: batchData,
        danjbh: isFromHome.value && danjbh ? danjbh : undefined,
      });

      createMessage.success(`成功复制 ${batchData.length} 条数据`);

      // 重新加载数据
      if (isInitialized.value) {
        tableRef.value?.commitProxy('query', {
          page: { currentPage: 1, pageSize: gridOptions.pagerConfig.pageSize },
        });
      }
    } catch (error) {
      console.error('批量复制失败:', error);
      createMessage.error('批量复制失败');
    }
  }

  function handleTableCancel() {
    // 关闭弹窗
    visible.value = false;

    // 如果是新增或复制操作，重新刷新表格数据，确保临时行被清除
    if ((optionType.value === 'add' || optionType.value === 'copy') && isInitialized.value) {
      tableRef.value?.commitProxy('query', {
        page: { currentPage: 1, pageSize: gridOptions.pagerConfig.pageSize },
      });
    }
  }

  function handleCard() {
    let currentRowIndex = tableRef.value?.getRowIndex(tableRef.value?.getCurrentRecord());
    let row = tableRef.value?.getCurrentRecord();
    if (currentRowIndex == undefined || currentRowIndex == -1) {
      currentRowIndex = 0;
      row = archiveData.value?.tablevalue[0];
    }
    optionType.value = 'edit';
    selectedRow.value = row;
    visible.value = true;
    tableRowIndex.value = currentRowIndex;
  }

  function _handleFileManagement() {
    const currentRow = tableRef.value?.getCurrentRecord();
    if (!currentRow || Object.keys(currentRow).filter((key) => key !== '_X_ROW_KEY').length === 0) {
      Modal.warning({
        title: '操作提示',
        content: '请先选择一行数据',
        okText: '确定',
      });
      return;
    }

    // 获取当前选中行的rowid，这里假设archiveData.fieldname是表的主键字段
    selectedRowId.value = currentRow[archiveData.value!.fieldname];
    fileManagementVisible.value = true;
  }

  function _handleSearch() {
    // 只有在初始化后才执行查询
    if (isInitialized.value) {
      // 执行表格查询，会自动触发proxyConfig.ajax.query方法
      tableRef.value?.commitProxy('query', {
        page: { currentPage: 1, pageSize: gridOptions.pagerConfig.pageSize },
      });
    }
  }

  function handleStep(step) {
    // 如果未初始化，不执行任何操作
    if (!isInitialized.value) return;

    // 此方法需要修改为使用后端分页API
    // 我们可以根据当前页和步骤计算新的页码
    const currentPage = tableRef.value?.getProxyInfo()?.pager?.currentPage || 1;
    const pageSize = tableRef.value?.getProxyInfo()?.pager?.pageSize || 10;
    const total = archiveData.value?.pagedData?.total || 0;

    switch (step) {
      case 'first':
        // 跳转到第一页

        tableRef.value?.commitProxy('query', { page: { currentPage: 1, pageSize } });
        break;
      case 'prev':
        // 跳转到上一页或当前页的前一条
        if (currentPage > 1) {
          tableRef.value?.commitProxy('query', {
            page: { currentPage: currentPage - 1, pageSize },
          });
        } else {
          // 已经在第一页了
          createMessage.warn('已经是第一页了！');
        }
        break;
      case 'next':
        // 跳转到下一页或当前页的后一条
        if (currentPage * pageSize < total) {
          tableRef.value?.commitProxy('query', {
            page: { currentPage: currentPage + 1, pageSize },
          });
        } else {
          // 已经在最后一页了
          createMessage.warn('已经是最后一页了！');
        }
        break;
      case 'last':
        // 跳转到最后一页
        const lastPage = Math.ceil(total / pageSize);
        tableRef.value?.commitProxy('query', { page: { currentPage: lastPage, pageSize } });
        break;
    }
  }

  const isPagination = ref(true);

  function _handlePaginationChange() {
    // 始终保持分页模式 - isPagination 在后端分页模式下不再需要
    isPagination.value = true;

    // 只有在初始化后才重新加载数据
    if (isInitialized.value) {
      tableRef.value?.commitProxy('query', {
        page: { currentPage: 1, pageSize: gridOptions.pagerConfig.pageSize },
      });
    }
  }

  const handleSelect = (selectedKeys: string[], { node: _node }) => {
    if (!selectedKeys.length) return;

    // 更新选中的树节点键值
    const selectedKey = selectedKeys[0];
    selectedTreeKey.value = selectedKey;

    // 只有在初始化后才重新查询数据
    if (isInitialized.value) {
      tableRef.value?.commitProxy('query', {
        page: { currentPage: 1, pageSize: gridOptions.pagerConfig.pageSize },
      });
    }
  };

  // 处理表头数据，确保tagfddisplayorder是数字类型
  function processTableHeader(headers: any[]): any[] {
    if (!headers || headers.length === 0) return [];

    return headers.map((header) => ({
      ...header,
      tagfddisplayorder:
        header.tagfddisplayorder !== null ? Number(header.tagfddisplayorder) : null,
    }));
  }

  function handleWorkflow() {
    // 获取单据编号
    const danjbh = route.query.danjbh as string;

    if (!danjbh) {
      Modal.warning({
        title: '操作提示',
        content: '无法获取单据编号，请先保存单据',
        okText: '确定',
      });
      return;
    }

    // 打开工作流弹窗
    showWorkflowModal.value = true;
  }

  // 处理审批提交
  async function handleApprovalSubmit(data: any) {
    try {
      await submitApproval(data);
      showWorkflowModal.value = false;
      Modal.success({
        title: '提交成功',
        content: '审批已提交成功',
        okText: '确定',
        onOk: () => {
          // 可以根据需要决定是否刷新页面或关闭页面
          // closeCurrent();
          if (isInitialized.value) {
            tableRef.value?.commitProxy('query');
          }
        },
      });
    } catch (error: any) {
      console.error('提交审批失败:', error);
      Modal.warn({
        title: '提交失败',
        content: error.message || '审批提交失败，请稍后重试',
        okText: '确定',
      });
    }
  }

  // 转换列数据为API所需格式
  function transformColumnData(data: any) {
    // 添加防御性检查，确保 data 不为空
    if (!data) return [];

    // 使用解构赋值时提供默认空对象，避免 undefined 错误
    const { resizableData = {}, sortData = {} } = data;
    const flds: Array<{
      fieldname: string;
      displayorder: number;
      customlength?: number;
    }> = [];

    console.log('转换列宽和顺序数据:', data);

    // 处理排序数据
    for (const [fieldName, order] of Object.entries(sortData)) {
      if (fieldName === 'type=checkbox') continue;
      const field: {
        fieldname: string;
        displayorder: number;
        customlength?: number;
      } = {
        fieldname: fieldName,
        displayorder: Number(order) || 0, // 确保是数字，防止 NaN
      };

      // 使用可选链和空值合并操作符防止 undefined 错误
      if (resizableData?.[fieldName]) {
        field.customlength = Number(resizableData[fieldName]) || 0;
      }

      flds.push(field);
    }

    console.log('转换后的列数据:', flds);
    return flds; // 返回字段列表，API需要接收一个列表
  }

  // 处理列设置按钮点击
  function handleColumnSettings() {
    setTimeout(() => {
      const $table = tableRef.value?.$refs.tableElRef as any;
      if ($table) {
        // 打开列设置弹窗前，先刷新表格列配置
        tableRef.value?.refreshColumn();

        // 确保列设置弹窗支持拖拽
        if ($table.openCustom) {
          $table.openCustom();
          console.log('已打开列设置弹窗，支持拖动调整顺序');

          // 监听列设置变化事件，确保保存到服务器
          const saveColumnSettings = () => {
            // 列设置保存是由 customConfig.updateStore 自动处理的
            console.log('列设置已变更，将自动保存');
            // 刷新列显示
            tableRef.value?.refreshColumn();
          };

          // 列设置弹窗关闭时触发
          const customCloseHandler = () => {
            saveColumnSettings();
            // 移除事件监听器
            document.removeEventListener('click', checkCustomClose);
          };

          // 检查是否关闭了列设置弹窗
          const checkCustomClose = (e) => {
            const customWrapper = document.querySelector('.vxe-custom--wrapper');
            if (!customWrapper || !customWrapper.contains(e.target)) {
              customCloseHandler();
              document.removeEventListener('click', checkCustomClose);
            }
          };

          // 添加一个延迟后监听点击事件
          setTimeout(() => {
            document.addEventListener('click', checkCustomClose);
          }, 300);
        } else {
          console.error('表格实例没有openCustom方法');
        }
      } else {
        console.error('无法获取表格实例');
      }
    }, 100);
  }
</script>

<style scoped lang="less">
  .ant-table-wrapper {
    height: 100%;
  }

  :deep(.vxe-table--body-wrapper) {
    overflow-x: auto !important;
  }

  :deep(.vben-page-wrapper-content) {
    margin: 0 !important;
    padding: 0 !important;
  }

  :deep(.ant-input) {
    height: 27px;
  }

  .button-group {
    display: flex;
    align-items: center;
    gap: 4px;

    :deep(.ant-btn) {
      height: 28px;
      padding: 4px 2px;
    }

    :deep(.ant-btn-link) {
      height: 32px;
      padding: 0 3px;
      transition: all 0.3s;
      color: #595959;
      font-size: 14px;

      &:hover {
        color: rgb(188 78 39);
      }

      .anticon {
        margin-right: 4px;
        font-size: 14px;
      }

      /* 覆盖 antd 的默认间距 */
      &.ant-btn > .anticon + span,
      &.ant-btn > span + .anticon {
        margin-inline-start: 0 !important;
      }
    }

    .divider {
      width: 1px;
      height: 14px;
      background-color: #d9d9d9;
    }
  }

  .horizontal-line {
    height: 1px;
    margin: 1px 0;
    background-color: #d9d9d9;
  }
</style>
